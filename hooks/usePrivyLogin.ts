import { useIdentityToken, usePrivy } from "@privy-io/react-auth";
import { useCallback, useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useCurrentAccount, useDisconnectWallet } from "@mysten/dapp-kit";
import { AppDispatch, RootState } from "@/store";
import { setUserAuth } from "@/store/user.store";
import Storage from "@/libs/storage";
import { LOGIN_METHODS, NETWORKS } from "@/utils/contants";
import { closeSocketInstance, createSocketInstance } from "@/libs/socket";
import rf from "@/services/RequestFactory";
import { useSessionSigners } from "@privy-io/react-auth";
import config from "@/config";

export const usePrivyLogin = () => {
  const { login, authenticated, user: privyUser, logout } = usePrivy();
  const { identityToken } = useIdentityToken();
  const dispatch = useDispatch<AppDispatch>();
  const currentAccount = useCurrentAccount();
  const { mutate: disconnect } = useDisconnectWallet();
  const userId = useSelector((state: RootState) => state.user.userId);
  const { addSessionSigners } = useSessionSigners();

  // const suiWallet = useMemo(() => {
  //   return
  // }, [privyUser?.wallet])

  const exchangePrivyForJWT = useCallback(async (idToken: string) => {
    try {
      const response = await rf.getRequest("PrivyRequest").login(idToken);
      return response.jwtToken;
    } catch (error) {
      console.error("Failed to exchange Privy token for JWT:", error);
      throw error;
    }
  }, []);

  const handlePrivyAuthSuccess = useCallback(async () => {
    if (!identityToken) {
      console.error("No identity token available");
      return;
    }

    try {
      const jwtToken = await exchangePrivyForJWT(identityToken);

      dispatch(setUserAuth({ accessToken: jwtToken }));

      if (currentAccount?.address) {
        disconnect();
      }

      Storage.setLoginMethod(LOGIN_METHODS.PRIVY);

      const redirectAfterLogin = Storage.getRedirectAfterLogin();
      if (redirectAfterLogin) {
        const location = `${window.location.pathname}${window.location.search}`;
        if (location !== redirectAfterLogin) {
          Storage.clearRedirectAfterLogin();
          window.location.href = redirectAfterLogin;
        }
      }

      // Reconnect socket with new JWT token (following Telegram login pattern)
      closeSocketInstance(NETWORKS.SUI);
      createSocketInstance(NETWORKS.SUI, jwtToken);

      console.log("Privy authentication successful");
    } catch (error) {
      console.error("Privy authentication failed:", error);
      // Optionally show error to user
    }
  }, [
    identityToken,
    exchangePrivyForJWT,
    dispatch,
    currentAccount?.address,
    disconnect,
  ]);

  // Auto-handle authentication when Privy login completes and identity token is available
  useEffect(() => {
    if (authenticated && identityToken && privyUser) {
      handlePrivyAuthSuccess();
    }
  }, [authenticated, identityToken, privyUser, handlePrivyAuthSuccess]);

  const onPrivyLogin = useCallback(() => {
    if (!authenticated) {
      login();
    } else {
      logout();
    }
  }, [login, authenticated]);

  const onPrivyLogout = useCallback(() => {
    logout();

    localStorage.removeItem("privy_sui_wallet");

    Storage.clearLoginMethod();
  }, [logout]);

  const createPrivyWallet = useCallback(async (numberWallets: number) => {
    try {
      const response = await rf
        .getRequest("PrivyRequest")
        .createWallet(numberWallets);

      handlerAddSignerSession(response[0]);
      return response;
    } catch (error) {
      console.error("Failed to create Privy wallet:", error);
      throw error;
    }
  }, []);

  const handlerAddSignerSession = async (address: string) => {
    try {
      const res = await addSessionSigners({
        signers: [
          {
            signerId: config.privyConfig.signerId,
          },
        ],
        address: address,
      });
      console.log(res, "add session success");
    } catch (error) {
      console.error("Failed to add Privy signer:", error);
    }
  };

  useEffect(() => {
    if (privyUser?.wallet?.address) {
      handlerAddSignerSession(privyUser?.wallet.address);
      return;
    }

    createPrivyWallet(2);
  }, [privyUser?.wallet, userId, createPrivyWallet]);

  return {
    onPrivyLogin,
    onPrivyLogout,
    createPrivyWallet,
    authenticated,
    user: privyUser,
    identityToken,
  };
};
